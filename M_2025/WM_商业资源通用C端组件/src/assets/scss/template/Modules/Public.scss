//> 图片未加载出来时 去除黑边框
img[src=""],
img:not([src]) {
  opacity: 0;
}

//> 数字字体专用标签 其它请勿使用 ins
ins {
  @extend %font-number;
}

// 缩放文字大小
.dlrHtml {
  font-size: calc(40 * 100vw / 750);
}

@media screen and (min-width: 600px) {
  .dlrHtml {
    font-size: 40px;
  }
}

//Flex布局 DIV滚动的
.dlrHtml {
  height: 100%;
  min-height: 100%;
  overflow: hidden; //> 重置body

  body {
    height: 100%;
    min-height: 100%;
    overflow: hidden; //>> 弹层弹起将body fixed起来

    &.dlrBody_fixed {
      position: fixed;
      top: 0;
      left: 0;
      bottom: 0;
      right: 0;
      width: 100%;
      height: 100%;
    }
  }

  //> VUE最外层的div 根元素
  .dlrContainer {
    width: 100%;
    height: 100%;
    min-height: 100%;
    box-sizing: border-box;
    max-width: 750px;
    margin: 0 auto;

    //>> flex布局 主内容区域
    &-wrapper {
      width: 100%;
      height: 100%;
      display: flex;
      flex-direction: column;

      //>>> 内容滚动区域
      &-scroll {
        flex: 1;
        overflow: hidden;
        overflow-y: auto;
        -webkit-overflow-scrolling: touch;
        overflow-scrolling: touch;
        background-color: #fff;
        position: relative;

        //>>> 锁住滚动
        &.dlrScroll_auto {
          -webkit-overflow-scrolling: auto;
          overflow-scrolling: auto;
        }
      }
    }
  }
}
//>>>>>>>>>>
//loading
@keyframes animLoad {
  from {
    transform: rotate(0deg);
    transform-origin: center center;
  }

  to {
    transform: rotate(360deg);
  }
}

.dlrCommon-load-more {
    margin-top: ptrd(20);
    height: ptrd(88);
    line-height: ptrd(88);
    text-align: center;
    font-size: ptrd(24);
    display: flex;
    align-items: center;
    justify-content: center;
    color: #c5cad4;
    box-sizing: border-box;
    em {
        width: ptrd(36);
        height: ptrd(36);
        animation: animLoad 1s infinite linear;
        // background: url($img + "icon/icon-loading_orange.png") no-repeat center center;
        background-size: ptrd(30);
        margin-right: ptrd(16px);
    }
}
.dlrCommon-load-end {
    margin-top: ptrd(20);
    height: ptrd(88);
    line-height: ptrd(88);
    text-align: center;
    font-size: ptrd(24);
    color: #c5cad4;
}

// 底部弹层
.dlr-layer--bottom {
  background: #fff;
  border-radius: ptrd(16px) ptrd(16px) 0px 0px;
  display: flex;
  /*! autoprefixer: off */
  display: -webkit-box;
  -webkit-box-orient: vertical;
  /* autoprefixer: on */
  flex-direction: column;
  min-height: ptrd(400px);
  max-height: 80%;
  padding-bottom: constant(safe-area-inset-bottom);
  padding-bottom: env(safe-area-inset-bottom);

  &__header {
    position: relative;
    height: ptrd(88px);
    flex-shrink: 0;
  }

  &__title {
    display: flex;
    align-items: center;
    justify-content: center;
    width: ptrd(480px);
    margin: 0 auto;
    font-size: ptrd(36px);
    font-weight: bold;
    height: ptrd(88px);

    h3 {
      @extend %ellipsis-basic;
    }
  }

  &__close {
    position: absolute;
    right: ptrd(10px);
    top: 0;
    width: ptrd(88px);
    height: ptrd(88px);
    background: url(https://fs.autohome.com.cn/dealer_views/dealer_front/m/m_dir/ics/fs_quotation/img/icon/icon-close_black-4906f0f8d5.png)
      no-repeat center center;
    background-size: ptrd(36px);
  }

  &__scroll {
    flex: 1;
    height: 100%;
    overflow-x: hidden;
    overflow-y: auto;
    box-sizing: border-box;
    // padding:ptrd(16px) ptrd(32px) 0;
    @extend %scrolling-touch;
  }

  &__footer {
    flex-shrink: 0;
    height: ptrd(136px);
    box-sizing: border-box;
  }

  &__btns {
    padding: ptrd(24px) ptrd(32px);
    display: flex;
    align-items: center;
  }

  &__btn {
    flex: 1;
    width: 1%;
    display: flex;
    align-items: center;
    justify-content: center;
    background-image: linear-gradient(90deg, #0099ff 0%, #0088ff 100%);
    border-radius: ptrd(4px);
    height: ptrd(96px);
    color: #fff;
    font-size: ptrd(28px);
    font-weight: bold;
  }
}

.dlrcolor-red {
  color: #ff230a;
}

//> 顶部
.pubsysTop {
  position: relative;
  z-index: 600;
  background: #fff;
  flex-shrink: 0;
  // height: ptrd(88px);
  //>> 顶部悬浮放在此DIV中

  &.fixedPubNav {
    // background-image: linear-gradient(270deg, #FF5770 27%, #FC7456 59%);
    // background-image: linear-gradient(-75deg, #FF5770  27%, #FC7456 59%);
    .pinkNav {
      // background-image: url($pubImgSrc + 'bg/skin/pink/nav-bg.png'+ $pubPngWebp) ;
      background: #ff5770;
    }
  }

  &-cont {
    position: absolute;
    // z-index: 0;
    top: calc(100% - #{ptrd(2)});
    left: 0;
    width: 100%;
    height: auto;
    box-sizing: border-box;
    opacity: 0;
    // transform: none !important;
    transform: translateY(-100%);
    transition-duration: 0.3s;

    //>> 动画显示
    &.animShow {
      opacity: 1;
      transform: translateY(0);
      // transform: none !important;
      transition-duration: 0.3s;
      // background-color: rgba(#fff, 0);
      // background-image: linear-gradient(270deg, #FC5A54 27%, #FC7456 59%);
    }
  }
}

//> 导航
.pubNav {
  position: relative;
  z-index: 10;
  height: ptrd(88);
  line-height: ptrd(88);
  padding: 0 ptrd(120) 0 ptrd(120);
  background: #fff;
  // &.pinkNav{
  //   background: url(https://fs.autohome.com.cn/dealer_views/dealer_front/m/s_dir/s_gulp_ics/jj22_fs_newtemai/img/skin_05/new_nav_bg-912672e034.png?format=webp) no-repeat;

  //   background-position: center top;
  //   background-repeat: no-repeat;
  //   background-size: 100% 100%;
  //   background-color: #FF5770;
  //   .pubNav-text{
  //     color: #fff;
  //   }
  //   .pubNav-return{
  //     background-image: url(https://fs.autohome.com.cn/dealer_views/dealer_front/m/s_dir/s_gulp_ics/s_public/ics_img/nav/0.0.0/ics_nav_return_02.png);
  //   }

  // }
  &-return {
    position: absolute;
    left: ptrd(16);
    top: 0;
    width: ptrd(88);
    height: ptrd(88);
    // background: url($img + "bg/customerHere/icon-back.png") no-repeat center center;
    background-size: ptrd(44);
  }

  &-close {
    position: absolute;
    left: ptrd(16);
    top: 0;
    width: ptrd(88);
    height: ptrd(88);
    background: url("https://fs.autohome.com.cn/dealer_views/dealer_front/m/s_dir/s_gulp_ics/s_public/ics_img/nav/0.0.0/ics_nav_close_01.png")
      no-repeat center center;
    background-size: ptrd(44);
  }

  &-text {
    display: block;
    text-align: center;
    font-size: ptrd(34);
    font-weight: bold;
    @extend %ellipsis-basic;
    color: #111e36;
  }

  &-share {
    position: absolute;
    right: ptrd(16);
    top: 0;
    width: ptrd(88);
    height: ptrd(88);
    // background: url($img + "bg/customerHere/icon-share.png") no-repeat center center;
    background-size: ptrd(44);
  }
}

//  中间弹层
.dlr-dialog {
  &__box {
    background: #fff;
    width: ptrd(560px);
    border-radius: ptrd(12px);
    padding: 0 ptrd(40px) ptrd(40px);
    box-sizing: border-box;
  }

  &__close {
    position: absolute;
    right: 0;
    top: 0;
    padding: 0 ptrd(24px) ptrd(24px) 0;

    em {
      display: inline-block;
      vertical-align: middle;
      width: ptrd(24px);
      height: ptrd(24px);
      background: url(https://fs.autohome.com.cn/dealer_views/dealer_front/m/m_dir/ics/fs_quotation/img/icon/icon-close_gray-f425d24558.png)
        no-repeat left bottom;
      background-size: 100%;
    }
  }

  &__title {
    font-size: ptrd(36px);
    line-height: ptrd(50);
    font-weight: bold;
    text-align: center;
    padding-top: ptrd(56px);
  }

  &__text {
    font-size: ptrd(28px);
    color: #828ca0;
    line-height: ptrd(44px);
    margin-top: ptrd(8px);
    text-align: center;

    &--left {
      text-align: left;
    }
  }

  //>> 购车时间
  &__time {
    .timeListul {
      li {
        margin-top: ptrd(20);
        background-color: #f8f9fc;
        border-radius: ptrd(8);
        box-sizing: border-box;
        border: ptrd(2) solid #f8f9fc;
        color: #666d7f;
        height: ptrd(80);

        &.current {
          border: ptrd(2) solid #ffd6cd;
          background-color: #ffefe6;
          font-weight: bold;
          color: #111e36;
        }

        label {
          box-sizing: border-box;
          padding: ptrd(20) ptrd(20);
          line-height: ptrd(40);
          display: block;
          font-size: ptrd(32);
        }

        .inpRadio {
          appearance: none;
          display: inline-block;
          vertical-align: middle;
          margin: ptrd(-4) ptrd(14) 0 0;
          width: ptrd(36);
          height: ptrd(36);
          border-radius: 50%;
          border: ptrd(3) solid #bec4cf;

          &:checked {
            background: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADIAAAAyBAMAAADsEZWCAAAAJFBMVEUAAAD///////////////////////////////////////////+0CY3pAAAAC3RSTlMAnUneHvjoZBSJJGtGKAYAAABUSURBVDjLYxgFIxko4ZJg3K2AQ0YaJoOpZSNOLQLoQlxpCji0sO8Owq6FgcN6qwIOW5yBmkBaMAGL9VZFqBZMTbuhWjA14fSLM0wLpvOmM4yC4QAAaogVV+M4bk8AAAAASUVORK5CYII=")
              no-repeat center center #ff6600;
            border: 0;
            background-size: ptrd(40);
          }
        }
      }
    }
  }
  .timeListul-tips {
    font-size: ptrd(24px);
    color: #464e64;
    line-height: ptrd(28);
    text-align: center;
    margin-top: ptrd(24);
  }
  &__buttons {
    margin-top: ptrd(32px);
    display: flex;
    align-items: center;
    margin-top: ptrd(24);
  }

  &__button {
    height: ptrd(80px);
    flex: 1;
    width: 1%;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    border-radius: ptrd(4px);
    font-size: ptrd(28px);
    background: linear-gradient(90deg, #f54e24 0%, #f54e24 100%);
    color: #fff;
    font-weight: bold;

    & + .dlr-dialog__button {
      margin-left: ptrd(16px);
    }

    &--hellow {
      background: #e5f3ff;
      color: #111e36;
    }
  }
}
.inline-block {
  display: inline-block;
  vertical-align: middle;
}

.flex {
  display: flex;
}
.flex-col {
  display: flex;
  flex-direction: column;
}
.flex-x-main {
  flex: 1;
  width: 1%;
}
.flex-shrink-0 {
  flex-shrink: 0;
}
.flex-y-center {
  display: flex;
  align-items: center;
}
.flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}
.flex-x-center {
  display: flex;
  justify-content: center;
}
.flex-y-end {
  display: flex;
  align-items: flex-end;
}
.justify-center {
  justify-content: center;
}
.justify-between {
  justify-content: space-between;
}
.flex-col-center {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}
.relative {
  position: relative;
}
.wh-full {
  width: 100%;
  height: 100%;
}
.w-full {
  width: 100%;
}
.h-full {
  height: 100%;
}
.white-bg {
  background: #fff;
}
