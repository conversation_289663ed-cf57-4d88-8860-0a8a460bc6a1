<script setup lang="ts">
withDefaults(defineProps<{
  direction?: 'bottom' | 'right' | 'middle'
  zIndex?: number
}>(), {
  direction: 'bottom',
  zIndex: 999,
})
const visible = defineModel<boolean>('visible', { default: false })
// 为了维护动画效果，额外引申的一个状态，组件内部维护
const _visible = ref(false)
const isAnimating = ref(false)

function show() {
  _visible.value = true
  setTimeout(() => {
    isAnimating.value = true
  }, 50)
}

function hide() {
  isAnimating.value = false
  setTimeout(() => {
    _visible.value = false
    visible.value = false
  }, 300)
}

watch(visible, (newVal) => {
  if (newVal) {
    show()
  }
  else {
    hide()
  }
})
</script>

<template>
  <ClientOnly>
    <teleport to="body">
      <div
        class="dlrLayer"
        :class="[
          {
            'fn-hide': !_visible,
          },
          direction === 'bottom' ? {
            slideBottom_in: isAnimating,
            slideBottom_out: !isAnimating,
          } : direction === 'right' ? {
            slideRight_in: isAnimating,
            slideRight_out: !isAnimating,
          } : {
            slideTop_in: isAnimating,
            slideTop_out: !isAnimating,
          },
        ]"
        :dlr-side-direction="direction"
        :style="{ zIndex }"
      >
        <slot />
        <div
          class="dlrLayer-mask"
          @click="hide"
        />
      </div>
    </teleport>
  </ClientOnly>
</template>

<style lang="scss" src="@/assets/scss/dealerMass3/3.4.0/module/_dlrLayerbase.scss"></style>
<style lang="scss">
// 为了避免通用的弹层组件多次引入样式，干脆让其直接跟随整个layer，有冗余，但是其他引入的时候不必再写一遍了
// 底部弹层
.dlr-layer--bottom {
  background: #fff;
  border-radius: ptrd(16px) ptrd(16px) 0px 0px;
  display: flex;
  /*! autoprefixer: off */
  display: -webkit-box;
  -webkit-box-orient: vertical;
  /* autoprefixer: on */
  flex-direction: column;
  min-height: ptrd(400px);
  max-height: 80%;
  padding-bottom: constant(safe-area-inset-bottom);
  padding-bottom: env(safe-area-inset-bottom);

  &__header {
    position: relative;
    height: ptrd(88px);
    flex-shrink: 0;
  }

  &__title {
    display: flex;
    align-items: center;
    justify-content: center;
    width: ptrd(480px);
    margin: 0 auto;
    font-size: ptrd(36px);
    font-weight: bold;
    height: ptrd(88px);

    h3 {
      @extend %ellipsis-basic;
    }
  }

  &__close {
    position: absolute;
    right: ptrd(10px);
    top: 0;
    width: ptrd(88px);
    height: ptrd(88px);
    background: url(https://fs.autohome.com.cn/dealer_views/dealer_front/m/m_dir/ics/fs_quotation/img/icon/icon-close_black-4906f0f8d5.png)
      no-repeat center center;
    background-size: ptrd(36px);
  }

  &__scroll {
    flex: 1;
    height: 100%;
    overflow-x: hidden;
    overflow-y: auto;
    box-sizing: border-box;
    // padding:ptrd(16px) ptrd(32px) 0;
    @extend %scrolling-touch;
  }

  &__footer {
    flex-shrink: 0;
    height: ptrd(136px);
    box-sizing: border-box;
  }

  &__btns {
    padding: ptrd(24px) ptrd(32px);
    display: flex;
    align-items: center;
  }

  &__btn {
    flex: 1;
    width: 1%;
    display: flex;
    align-items: center;
    justify-content: center;
    background-image: linear-gradient(90deg, #0099ff 0%, #0088ff 100%);
    border-radius: ptrd(4px);
    height: ptrd(96px);
    color: #fff;
    font-size: ptrd(28px);
    font-weight: bold;
  }
}
//  中间弹层
.dlr-dialog {
  &__box {
    background: #fff;
    width: ptrd(560px);
    border-radius: ptrd(12px);
    padding: 0 ptrd(40px) ptrd(40px);
    box-sizing: border-box;
  }

  &__close {
    position: absolute;
    right: 0;
    top: 0;
    padding: 0 ptrd(24px) ptrd(24px) 0;

    em {
      display: inline-block;
      vertical-align: middle;
      width: ptrd(24px);
      height: ptrd(24px);
      background: url(https://fs.autohome.com.cn/dealer_views/dealer_front/m/m_dir/ics/fs_quotation/img/icon/icon-close_gray-f425d24558.png)
        no-repeat left bottom;
      background-size: 100%;
    }
  }

  &__title {
    font-size: ptrd(36px);
    line-height: ptrd(50);
    font-weight: bold;
    text-align: center;
    padding-top: ptrd(56px);
  }

  &__text {
    font-size: ptrd(28px);
    color: #828ca0;
    line-height: ptrd(44px);
    margin-top: ptrd(8px);
    text-align: center;

    &--left {
      text-align: left;
    }
  }

  &__buttons {
    margin-top: ptrd(32px);
    display: flex;
    align-items: center;
    margin-top: ptrd(24);
  }

  &__button {
    height: ptrd(80px);
    flex: 1;
    width: 1%;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    border-radius: ptrd(4px);
    font-size: ptrd(28px);
    background: linear-gradient(90deg, #f54e24 0%, #f54e24 100%);
    color: #fff;
    font-weight: bold;

    & + .dlr-dialog__button {
      margin-left: ptrd(16px);
    }

    &--hellow {
      background: #e5f3ff;
      color: #111e36;
    }
  }
}
</style>