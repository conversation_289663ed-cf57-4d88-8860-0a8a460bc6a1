<template>
  <!-- 本表单为静态展示表单，请不要在这里添加逻辑，方便后续查找需要的dom -->
  <div class="dlr-form">
    <ul class="dlr-form__ul">
      <li>
        <div class="dlr-form__item">
          <p class="dlr-form__label">
            购车方式
          </p>
          <div class="dlr-form__content">
            <div class="dlr-form__slts">
              <p class="current">
                <span>北京</span>
              </p>
              <p><span>其他</span></p>
            </div>
          </div>
        </div>
      </li>
      <li>
        <div class="dlr-form__item">
          <p class="dlr-form__label">
            是否置换
          </p>
          <div class="dlr-form__content">
            <div class="dlr-form__slts">
              <p class="current">
                <span>北京</span>
              </p>
              <p><span>其他</span></p>
            </div>
          </div>
        </div>
      </li>
      <li>
        <div class="dlr-form__item">
          <p class="dlr-form__label">
            试驾车型
          </p>
          <div class="dlr-form__content">
            <div class="dlr-form__card">
              <p class="dlr-form__card-text selected">
                <span>北京</span>
              </p>
            </div>
            <p class="dlr-form__icon">
              <i class="dlr-form__icon-right" />
            </p>
          </div>
        </div>
      </li>
      <li>
        <div class="dlr-form__item">
          <p class="dlr-form__label">
            姓名
          </p>
          <div class="dlr-form__content">
            <div class="dlr-form__card">
              <p class="dlr-form__input">
                <input
                  type="text"
                  class="dlr-form__input-text"
                  placeholder="请输入姓名"
                >
              </p>
            </div>
            <p class="dlr-form__icon">
              <i class="dlr-form__icon-close" />
            </p>
          </div>
        </div>
      </li>
      <li>
        <div class="dlr-form__item">
          <p class="dlr-form__label">
            手机号码
          </p>
          <div class="dlr-form__content">
            <div class="dlr-form__card">
              <p class="dlr-form__input">
                <input
                  type="text"
                  class="dlr-form__input-text"
                  value="186****9090"
                  placeholder="请输入电话号码"
                >
              </p>
            </div>
            <p class="dlr-form__icon">
              <i class="dlr-form__icon-close" />
            </p>
          </div>
        </div>
      </li>
      <li>
        <div class="dlr-form__item">
          <p class="dlr-form__label">
            手机号码
          </p>
          <div class="dlr-form__content">
            <div class="dlr-form__card">
              <p class="dlr-form__input">
                <input
                  type="text"
                  class="dlr-form__input-text"
                  value="186****9090"
                  placeholder="请输入电话号码"
                >
              </p>
            </div>
            <div class="dlr-form__other">
              <p class="dlr-form__other-text">
                已加密保护
              </p>
            </div>
          </div>
        </div>
      </li>
      <li>
        <div class="dlr-form__item">
          <p class="dlr-form__label">
            验证码
          </p>
          <div class="dlr-form__content">
            <div class="dlr-form__card">
              <p class="dlr-form__input">
                <input
                  type="text"
                  class="dlr-form__input-text"
                  placeholder="请输入验证码"
                >
              </p>
            </div>
            <div class="dlr-form__other">
              <p class="dlr-form__code">
                获取验证码
              </p>
            </div>
          </div>
        </div>
      </li>
      <li>
        <div class="dlr-form__item">
          <p class="dlr-form__label">
            验证码
          </p>
          <div class="dlr-form__content">
            <div class="dlr-form__card">
              <p class="dlr-form__input">
                <input
                  type="text"
                  class="dlr-form__input-text"
                  placeholder="请输入验证码"
                >
              </p>
            </div>
            <div class="dlr-form__other">
              <p class="dlr-form__code">
                58S
              </p>
            </div>
          </div>
        </div>
      </li>
      <li>
        <div class="dlr-form__item">
          <p class="dlr-form__label">
            验证码
          </p>
          <div class="dlr-form__content">
            <div class="dlr-form__card">
              <p class="dlr-form__input">
                <input
                  type="text"
                  class="dlr-form__input-text"
                  placeholder="请输入验证码"
                >
              </p>
            </div>
            <div class="dlr-form__other">
              <p class="dlr-form__code">
                重新获取
              </p>
            </div>
          </div>
        </div>
      </li>
      <li>
        <div class="dlr-form__item">
          <p class="dlr-form__label">
            上牌区域
          </p>
          <div class="dlr-form__content">
            <div class="dlr-form__card">
              <p class="dlr-form__card-text selected">
                <span>北京</span>
              </p>
            </div>
            <p class="dlr-form__icon">
              <i class="dlr-form__icon-right" />
            </p>
          </div>
        </div>
      </li>
      <li>
        <div class="dlr-form__item">
          <p class="dlr-form__label">
            上牌区域
          </p>
          <div class="dlr-form__content">
            <div class="dlr-form__card">
              <p class="dlr-form__card-text">
                <span>请选择</span>
              </p>
            </div>
            <p class="dlr-form__icon">
              <i class="dlr-form__icon-right" />
            </p>
          </div>
        </div>
      </li>
      <li>
        <div class="dlr-form__item dlr-form__textarea">
          <p class="dlr-form__label">
            收货地址
          </p>
          <div class="dlr-form__content">
            <textarea id="" class="dlr-form__textarea-input" name="" placeholder="请输入收货地址" />
          </div>
        </div>
      </li>
    </ul>
    <div class="dlr-form__statement">
      <label>
        <input type="checkbox" class="dlr-form__statement-check">
        <span>已阅读并同意</span>
      </label>
      <em>《汽车之家隐私政策-询价功能》</em>
    </div>
    <div class="dlr-form__submit disabled">
      <p class="dlr-form__submit--default">
        <span class="dlr-form__submit-text">预约试驾</span>
      </p>
    </div>
  </div>
</template>

<style lang="scss">
%formIconBase {
  display: inline-block;
  vertical-align: middle;
  width: ptrd(40);
  height: ptrd(40);
}
.dlr-form {
  &__ul {
    &.dlr-form__height100 {
      .dlr-form__item {
        line-height: ptrd(48);
        padding: ptrd(26px) 0;
        font-size: ptrd(32px);
      }
      .dlr-form__input-text {
        font-size: ptrd(32px);
      }
      .dlr-form__textarea-input {
        font-size: ptrd(32px);
      }
    }
  }
  &__item {
    display: flex;
    line-height: ptrd(56);
    box-sizing: border-box;
    padding: ptrd(16) 0;
    border-bottom: 1px solid rgba(#f1f3f6, 0.5);
    font-size: ptrd(28);
    &.dlr-form__textarea {
      flex-direction: column;
    }
  }
  /*
    *   左侧名称
    */
  &__label {
    width: ptrd(188);
  }

  /*
    *   右侧名称 flex 1
    */
  &__content {
    flex: 1;
    width: 100%;
    display: flex;
    align-items: center;
  }

  /*
    *   第一行带输入框 右侧带ICON
    */
  &__card {
    display: inline-flex;
    flex-direction: column;
    flex: 1;
    width: 1%;
  }
  /*
    *    input 输入框
    */
  &__input {
    height: ptrd(40);
    line-height: ptrd(40);
    padding-right: ptrd(16px);
  }
  &__input-text {
    display: inline-block;
    vertical-align: top;
    width: 100%;
    height: ptrd(40);
    line-height: ptrd(40);
    box-sizing: border-box;
    font-size: ptrd(28);
    &::-webkit-input-placeholder {
      color: #c5cad4;
    }

    &:disabled {
      background-color: #fff;
      color: #c5cad4;
    }
  }
  /*
    *    右侧图标
    */

  &__icon {
    height: ptrd(40);
    flex-shrink: 0;
    font-size: 0;
    i + i {
      margin-left: ptrd(4);
    }
    &-close {
      @extend %formIconBase;
      background: url(https://fs.autohome.com.cn/dealer_views/dealer_front/m/s_dir/s_gulp_ics/jj23_fs_baojiadan/img/skin_default/dlrnewform/dlrnewform_icon_04-64315d6094.png)
        no-repeat center center;
      background-size: ptrd(32);
    }

    &-shield {
      position: relative;
      @extend %formIconBase;
      background: url(https://fs.autohome.com.cn/dealer_views/dealer_front/m/s_dir/s_gulp_ics/jj23_fs_baojiadan/img/skin_default/dlrnewform/dlrnewform_icon_03-e9de846f76.png)
        no-repeat center center;
      background-size: ptrd(32);
    }
    &-right {
      @extend %formIconBase;
      background: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADIAAAAyBAMAAADsEZWCAAAAKlBMVEUAAABmboBnbYBmbYB2eoJnbYBmbYBnbYBob4FnboFocIJnbYBtbYBmbX960ZZwAAAADXRSTlMAxfKxCuKYfmVPOyoc7fE/ygAAAFxJREFUOMtjGAUDAQwdcMnIiuCSOXoZlwy3bgIuqUXXcclw3m3AJdV7E5cM+6UFuKRytXHJMF89gEOGRVYAtwzJpuVqk+zq3pskh86i67hjgeSYkxXBk0JGwcAAAI+FFeMOxChcAAAAAElFTkSuQmCC)
        no-repeat center center;
      background-size: ptrd(44);
    }
    &-qa {
      @extend %formIconBase;
      background: url(https://fs.autohome.com.cn/dealer_views/dealer_front/m/m_dir/ics/fs_quotation/img/icon/icon-qa_blue.png)
        no-repeat center center;
      background-size: ptrd(28);
    }
  }

  /*
    *   获取验证码等
    */
  &__other {
    flex-shrink: 0;
    &-text {
      color: #c5cad4;
      font-size: ptrd(28);
    }
  }
  &__code {
    color: #0088ff;
  }
  &__captcha {
    width: ptrd(128px);
    font-size: 0;
    img {
      display: inline-block;
      width: 100%;
      vertical-align: middle;
    }
  }

  /*
    *   文本选择
    */
  &__card-text {
    color: #c5cad4;
    display: flex;
    align-items: center;
    &.selected {
      color: #111e36;
    }
  }
  &__card-msg {
    font-size: ptrd(24px);
    color: #828ca0;
    line-height: ptrd(32px);
    margin-top: ptrd(4px);
  }
  /*
    *   input checkbox switch切换
    */
  &__switch {
    height: ptrd(40px);
  }
  &__inpSwitch {
    appearance: none;
    width: ptrd(72);
    height: ptrd(40);
    border-radius: ptrd(40);
    background: url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAD4AAAA+CAMAAABEH1h2AAAAP1BMVEUAAAAAAAAAAAAnJycBAQH+/v79/f36+vr09PRVVVX39/fw8PDq6urm5ubj4+Pd3d3U1NTR0dG/v7+goKD////l1WhlAAAAFHRSTlMCBwwXEu/k2LYfyKWRhoByX1lHMzDR+IkAAAFNSURBVEjHpZfZtoMwCEXtxUxax/L/33qVmNV0OWQ4+30DpSaB5oJX4E94BZocYrPdiGPkySJSRNuGCGnZu+vYd1oxK9314yoRfICkTDRbxT8oOxM9BwiyGxRfoAYXAjzZk+Yb9HTvH/Ji+AGzHAFu7DcneAf/wnaWk1jn/bP9MZyB+Xj/ZCvOQgU/1ltnOBPjWtFjmyxnYyn299JPPU/0fy8/Sr5wEcs3vZRuynQj5YfkNHEhEx3ppeu6VNfS/SP5wMUMkt7/clWuK/n1os9cwRx0sjW6JdF97XXVi75yFavXaazTR/J6X6f3h97V6d2ub4dN1+l6O3ZNbeOl9aJzJbCOFg+2Dvnj0M8G/GjRIwMeWOi6QC8r+KoEL2rsmQAfKeyJBB9obDwAhxNsNAIHM2wsxIZSbCTGBnJsHcCWEXQVwhcxfA3El1BwBf4H3gXE0Y+BG+YAAAAASUVORK5CYII=')
      no-repeat #e6ebf5 0 center;
    background-size: ptrd(40);
    transition-duration: 0.3s;

    &:checked {
      background-color: #0088ff;
      background-position: ptrd(32) center;
      transition-duration: 0.3s;
    }
  }

  /*
    *    文本框
    */
  &__textarea-input {
    width: 100%;
    padding: ptrd(12) 0 ptrd(6) 0;
    box-sizing: border-box;
    font-size: ptrd(28);
    line-height: ptrd(40);
    resize: none;
    &::-webkit-input-placeholder {
      color: #c5cad4;
    }
    &.error {
      color: #fe4a3b;

      &::-webkit-input-placeholder {
        color: #fe4a3b;
      }
    }
  }

  /*
    *   提交按钮
    */
  &__submit {
    margin-top: ptrd(24);
    background-image: linear-gradient(90deg, #0099ff 0%, #0088ff 100%);
    color: #fff;
    border-radius: ptrd(4);
    text-align: center;
    transition-duration: 0.3s;
    &.disabled {
      opacity: 0.5;
    }
    &--default {
      height: ptrd(96);
      display: flex;
      flex-direction: column;
      justify-content: center;
    }
    &-text {
      height: ptrd(40);
      line-height: ptrd(40);
      font-size: ptrd(32);
      font-weight: bold;
    }

    &-msg {
      margin-top: ptrd(4);
      height: ptrd(28);
      line-height: ptrd(28);
      font-size: ptrd(20);
      font-weight: normal;
    }
  }
  /*
    *    声明
    */
  &__statement {
    margin: ptrd(20) 0 0 0;
    line-height: ptrd(28);
    color: #828ca0;
    font-size: 0;
    &-check {
      appearance: none;
      margin: 0 ptrd(8) 0 0;
      width: ptrd(24);
      height: ptrd(24);
      border: ptrd(3) solid #c5cad4;
      box-sizing: border-box;
      border-radius: ptrd(16);
      background-position: center center;
      background-size: 0;
      transition-duration: 0.3s;
      position: relative;
      top: ptrd(4px);
      &:checked {
        border: 0;
        background: url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADIAAAAyBAMAAADsEZWCAAAAJFBMVEUAAAD///////////////////////////////////////////+0CY3pAAAAC3RSTlMAnUneHvjoZBSJJGtGKAYAAABUSURBVDjLYxgFIxko4ZJg3K2AQ0YaJoOpZSNOLQLoQlxpCji0sO8Owq6FgcN6qwIOW5yBmkBaMAGL9VZFqBZMTbuhWjA14fSLM0wLpvOmM4yC4QAAaogVV+M4bk8AAAAASUVORK5CYII=')
          no-repeat center center #0088ff;
        background-size: ptrd(36);
        transition-duration: 0.3s;
      }
    }
    &-check,
    span,
    em,
    a {
      font-style: normal;
      font-size: ptrd(20);
    }

    em,
    a {
      color: #464e64;
      text-decoration: none;
      -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
      tap-highlight-color: rgba(0, 0, 0, 0);
    }
    /*居中显示*/
    &.dlr-form__statement--center {
      text-align: center;
    }
    /* 橙色样式*/
    &.dlr-form__statement--orange {
      .dlr-form__statement-check {
        &:checked {
          background-color: #fa5b20;
        }
      }
    }
  }

  .dlr-form__slts {
    display: flex;
    align-items: center;
    p {
      border-radius: ptrd(4px);
      background: #f8f9fc;
      font-size: ptrd(24);
      min-width: ptrd(96);
      height: ptrd(56);
      text-align: center;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-right: ptrd(16);
      padding: 0 ptrd(8);
      box-sizing: border-box;
      span {
        @extend %ellipsis-basic;
      }
      &.current {
        background: rgba(#0088ff, 0.08);
      }
    }
  }
}
</style>
