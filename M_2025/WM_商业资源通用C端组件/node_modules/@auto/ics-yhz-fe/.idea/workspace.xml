<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="ChangeListManager">
    <list default="true" id="707755b5-692a-49ec-9b18-c2f3e4320f91" name="Default Changelist" comment="" />
    <option name="EXCLUDED_CONVERTED_TO_IGNORED" value="true" />
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
    <option name="RECENT_BRANCH_BY_REPOSITORY">
      <map>
        <entry key="$PROJECT_DIR$" value="feature/页面返回增加鸿蒙支持" />
      </map>
    </option>
  </component>
  <component name="MavenImportPreferences">
    <option name="generalSettings">
      <MavenGeneralSettings>
        <option name="localRepository" value="D:\Program Files\repository" />
        <option name="mavenHome" value="D:/Program Files/maven-3.6.0" />
        <option name="userSettingsFile" value="D:\Program Files\repository\settings.xml" />
      </MavenGeneralSettings>
    </option>
  </component>
  <component name="ProjectConfigurationFiles">
    <option name="files">
      <list>
        <option value="$PROJECT_DIR$/.idea/ics-yhz-fe-v2.iml" />
        <option value="$PROJECT_DIR$/.idea/misc.xml" />
        <option value="$PROJECT_DIR$/.idea/vcs.xml" />
        <option value="$PROJECT_DIR$/.idea/modules.xml" />
      </list>
    </option>
  </component>
  <component name="ProjectFrameBounds" extendedState="6">
    <option name="x" value="448" />
    <option name="y" value="222" />
    <option name="width" value="2558" />
    <option name="height" value="1390" />
  </component>
  <component name="ProjectView">
    <navigator proportions="" version="1">
      <foldersAlwaysOnTop value="true" />
    </navigator>
    <panes>
      <pane id="PackagesPane" />
      <pane id="Scope" />
      <pane id="ProjectPane">
        <subPane>
          <expand>
            <path>
              <item name="ics-yhz-fe-v2" type="b2602c69:ProjectViewProjectNode" />
              <item name="ics-yhz-fe-v2" type="462c0819:PsiDirectoryNode" />
            </path>
            <path>
              <item name="ics-yhz-fe-v2" type="b2602c69:ProjectViewProjectNode" />
              <item name="ics-yhz-fe-v2" type="462c0819:PsiDirectoryNode" />
              <item name="src" type="462c0819:PsiDirectoryNode" />
            </path>
            <path>
              <item name="ics-yhz-fe-v2" type="b2602c69:ProjectViewProjectNode" />
              <item name="ics-yhz-fe-v2" type="462c0819:PsiDirectoryNode" />
              <item name="src" type="462c0819:PsiDirectoryNode" />
              <item name="auto-app" type="462c0819:PsiDirectoryNode" />
            </path>
          </expand>
          <select />
        </subPane>
      </pane>
    </panes>
  </component>
  <component name="PropertiesComponent">
    <property name="WebServerToolWindowFactoryState" value="false" />
    <property name="aspect.path.notification.shown" value="true" />
    <property name="last_opened_file_path" value="$PROJECT_DIR$" />
    <property name="nodejs_interpreter_path.stuck_in_default_project" value="undefined stuck path" />
    <property name="nodejs_npm_path_reset_for_default_project" value="true" />
    <property name="nodejs_package_manager_path" value="npm" />
    <property name="settings.editor.selected.configurable" value="editor.preferences.fonts.default" />
    <property name="ts.external.directory.path" value="D:\Program Files\JetBrains\IntelliJ IDEA 2019.1.3\plugins\JavaScriptLanguage\jsLanguageServicesImpl\external" />
  </component>
  <component name="RunDashboard">
    <option name="ruleStates">
      <list>
        <RuleState>
          <option name="name" value="ConfigurationTypeDashboardGroupingRule" />
        </RuleState>
        <RuleState>
          <option name="name" value="StatusDashboardGroupingRule" />
        </RuleState>
      </list>
    </option>
  </component>
  <component name="SvnConfiguration">
    <configuration />
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="707755b5-692a-49ec-9b18-c2f3e4320f91" name="Default Changelist" comment="" />
      <created>1729067239984</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1729067239984</updated>
      <workItem from="1729067241131" duration="2045000" />
    </task>
    <task id="LOCAL-00001" summary="鸿蒙平台判断">
      <created>1729068862268</created>
      <option name="number" value="00001" />
      <option name="presentableId" value="LOCAL-00001" />
      <option name="project" value="LOCAL" />
      <updated>1729068862268</updated>
    </task>
    <option name="localTasksCounter" value="2" />
    <servers />
  </component>
  <component name="TimeTrackingManager">
    <option name="totallyTimeSpent" value="2045000" />
  </component>
  <component name="ToolWindowManager">
    <frame x="-8" y="-8" width="2576" height="1408" extended-state="6" />
    <layout>
      <window_info id="Image Layers" />
      <window_info id="Designer" />
      <window_info id="UI Designer" />
      <window_info id="Capture Tool" />
      <window_info id="Favorites" side_tool="true" />
      <window_info content_ui="combo" id="Project" order="0" visible="true" weight="0.25" />
      <window_info id="Structure" order="1" side_tool="true" weight="0.25" />
      <window_info anchor="bottom" id="Docker" show_stripe_button="false" />
      <window_info anchor="bottom" id="Database Changes" />
      <window_info anchor="bottom" id="Version Control" weight="0.3296" />
      <window_info active="true" anchor="bottom" id="Terminal" visible="true" weight="0.3296" />
      <window_info anchor="bottom" id="Event Log" side_tool="true" />
      <window_info anchor="bottom" id="TypeScript" />
      <window_info anchor="bottom" id="Message" order="0" />
      <window_info anchor="bottom" id="Find" order="1" />
      <window_info anchor="bottom" id="Run" order="2" />
      <window_info anchor="bottom" id="Debug" order="3" weight="0.4" />
      <window_info anchor="bottom" id="Cvs" order="4" weight="0.25" />
      <window_info anchor="bottom" id="Inspection" order="5" weight="0.4" />
      <window_info anchor="bottom" id="TODO" order="6" />
      <window_info anchor="right" id="Palette" />
      <window_info anchor="right" id="Theme Preview" />
      <window_info anchor="right" id="Maven" />
      <window_info anchor="right" id="Capture Analysis" />
      <window_info anchor="right" id="Palette&#9;" />
      <window_info anchor="right" id="Database" />
      <window_info anchor="right" id="Commander" internal_type="SLIDING" order="0" type="SLIDING" weight="0.4" />
      <window_info anchor="right" id="Ant Build" order="1" weight="0.25" />
      <window_info anchor="right" content_ui="combo" id="Hierarchy" order="2" weight="0.25" />
    </layout>
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="1" />
  </component>
  <component name="VcsManagerConfiguration">
    <MESSAGE value="鸿蒙平台判断" />
    <option name="LAST_COMMIT_MESSAGE" value="鸿蒙平台判断" />
  </component>
  <component name="editorHistoryManager">
    <entry file="file://$PROJECT_DIR$/src/auto-app/onDisplayWhenBackInApp.ts">
      <provider selected="true" editor-type-id="text-editor" />
    </entry>
  </component>
  <component name="masterDetails">
    <states>
      <state key="ProjectJDKs.UI">
        <settings>
          <last-edited>1.8</last-edited>
          <splitter-proportions>
            <option name="proportions">
              <list>
                <option value="0.2" />
              </list>
            </option>
          </splitter-proportions>
        </settings>
      </state>
    </states>
  </component>
</project>