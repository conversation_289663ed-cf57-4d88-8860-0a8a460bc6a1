import { MapLonLat } from "./MapLonLat";
export declare type OpenAmapInfoWindowParams = {
    AMap?: any;
    instance: any;
    position: {
        isBaiduPos?: boolean;
    } & MapLonLat;
    content: string;
    /**
     * 信息窗体锚点的设置
     */
    anchor?: 'top-left' | 'top-center' | 'top-right' | 'middle-left' | 'center' | 'middle-right' | 'bottom-left' | 'bottom-center' | 'bottom-right';
    isCustom?: boolean;
    offset?: {
        left: number;
        top: number;
    };
    showShadow?: boolean;
};
export interface OpenAmapInfoWindowResult {
    infoWindow: any;
}
/**
 * 高德地图增加信息弹窗
 * <AUTHOR>
 * @date 2022-04-29
 */
export declare function openAmapInfoWindow(params: OpenAmapInfoWindowParams): OpenAmapInfoWindowResult;
