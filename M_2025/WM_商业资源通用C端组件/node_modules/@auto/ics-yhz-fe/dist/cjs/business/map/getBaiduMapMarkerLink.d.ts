import { MapLonLat } from "./MapLonLat";
/**
 * 获取百度地图标点链接
 * * 文档: https://lbsyun.baidu.com/index.php?title=uri/api/web
 * * 返回示例: http://api.map.baidu.com/marker?location=40.047669,116.313082&title=我的位置&content=百度奎科大厦&output=html&src=webapp.baidu.openAPIdemo
 * <AUTHOR>
 * @date 2022-04-27
 */
export declare function getBaiduMapMarkerLink(params: GetBaiduMapMarkerLinkParams): string;
export declare type GetBaiduMapMarkerLinkParams = {
    /**
     * 标注点显示标题
     */
    title: string;
    /**
     * 标注点显示内容
     */
    content: string;
    /**
     * 是统计必选参数，格式为：andr.companyName.appName。不传此参数，不保证服务
     */
    src: string;
    /**
     * 表示输出类型，web上必须指定为html才能展现地图产品结果。
     */
    output?: string;
    /**
     * 展现地图的级别，默认为视觉最优级别。
     */
    zoom?: string;
    /**
     * 坐标类型，可选参数。
     * 允许的值为：
     * * bd09ll（百度经纬度坐标）
     * * bd09mc（百度墨卡托坐标）
     * * gcj02（经国测局加密的坐标)
     * * wgs84（gps获取的原始坐标）
     */
    coord_type?: "bd09ll" | "bd09mc" | "gcj02" | "wgs84";
} & MapLonLat;
