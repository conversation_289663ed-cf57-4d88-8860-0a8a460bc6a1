import { Protocol } from "../models/index";
declare type AppSyncCityResult = Protocol<{
    cityId: string;
    cityName: string;
    provinceId: string;
    provinceName: string;
    /**
     * 返回获取城市信息对应的类型。0：默认  1：用户选择方式  2：缓存方式  3：IP方式    10.20.5及以后版本支持
     */
    type: "0" | "1" | "2" | "3";
}>;
/**
 * 主App 获取用户城市
 * @see http://wiki.corpautohome.com/pages/viewpage.action?pageId=109350555
 * @param timeout - 超时时间(默认2s). PS: 设置超时时间是为了防止 success/fail 回调均未执行. 在不支持getSyncCity的老版本中可能出现.
 * @public
*/
export declare function getAppSyncCity(timeout?: number): Promise<AppSyncCityResult>;
export {};
