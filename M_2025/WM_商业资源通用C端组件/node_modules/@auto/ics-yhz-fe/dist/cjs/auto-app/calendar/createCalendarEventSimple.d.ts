/**
 * 创建简单日历
 *
 * @summary 使用示例: `createCalendarEventSimple({ title: "事件标题", note: "事件备注", startTime: new window.Moment("2023-10-12").toDate(), endTime: new window.Moment("2023-10-13").toDate() })`
 * <AUTHOR>
 * @date 2023-10-26
 */
export declare function createCalendarEventSimple(params: CreateCalendarEventSimpleParams): Promise<void>;
export declare type CreateCalendarEventSimpleParams = {
    permissions?: {
        read: string;
        write: string;
    };
    /**
     * 日历标题
     */
    title: string;
    /**
     * 备注
     */
    note?: string;
    /**
     * 开始时间
     */
    startTime: Date | string;
    /**
     * 结束时间
     */
    endTime: Date | string;
    /**
     * 提醒时间(单位分钟)
     */
    alarmMinutes?: number;
};
