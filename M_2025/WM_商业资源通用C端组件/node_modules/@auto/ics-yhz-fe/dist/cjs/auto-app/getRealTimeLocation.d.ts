import { Protocol } from "../models";
/**
 * H5获取Native实时定位信息
 * @param permissionDesc 权限申请说明文案
 * @link http://wiki.corpautohome.com/pages/viewpage.action?pageId=89742444
 */
export declare function getRealTimeLocation(permissionDesc?: string, timeout?: number): Promise<Protocol<RealTimeLocationResult>>;
export declare type RealTimeLocationResult = {
    /**
     * 定位结果类型，值为0表示默认定位结果，值为1表示缓存定位结果，值为2表示实时定位结果
     */
    type: string;
    WGSLongitude: string;
    WGSLatitude: string;
    GCJLongitude: string;
    GCJLatitude: string;
    BDLongitude: string;
    BDLatitude: string;
    provinceid: string;
    provincename: string;
    /**
     * 城市Id，默认值为字符串0
     */
    cityid: string;
    cityname: string;
    districtid: string;
    districtname: string;
};
