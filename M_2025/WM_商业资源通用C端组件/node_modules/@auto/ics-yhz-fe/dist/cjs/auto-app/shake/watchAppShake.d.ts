import { OnAppShakeCallback, OnAppShakeResult } from "./onAppShake";
import { Protocol } from "../../models";
export declare type WatchAppShakeParams = {
    /**
     * 监听主软摇一摇的回调函数(注意 iOS 平台可能触发两次回调函数)
     */
    watch: OnAppShakeCallback;
};
export declare type WatchAppShakeResult = OnAppShakeResult & {
    /**
     *
     */
    stopShake: () => void;
};
export declare type WatchAppShakeDef = {
    (params: WatchAppShakeParams): Protocol<WatchAppShakeResult>;
    __hasStart?: boolean;
};
/**
 * 监听App摇一摇动作.
 *
 * *该方法封装了三个底层方法(`startAppShake`, `onAppShake`, `stopAppShake`), 直接使用该方法即可监听摇一摇动作*
 *
 * * wiki: http://wiki.corpautohome.com/pages/viewpage.action?pageId=107320967
 * * iOS端，当弹出两个及两个以上通用浏览器页面，都开启监听，摇一摇时，先弹出的通用浏览器页面，不会接收到摇一摇回调，最后出现的通用浏览器界面会接收到摇一摇回调，安卓不支持相应逻辑。
 * * 由于摇一摇为原生、H5以及RN共有，所以在H5页面退出时保证进行释放，否则一直在监听摇一摇，造成资源浪费。(调用返回结果里的 `stopShake` 方法)
 * <AUTHOR>
 * @date 2023-01-06
 */
export declare const watchAppShake: WatchAppShakeDef;
