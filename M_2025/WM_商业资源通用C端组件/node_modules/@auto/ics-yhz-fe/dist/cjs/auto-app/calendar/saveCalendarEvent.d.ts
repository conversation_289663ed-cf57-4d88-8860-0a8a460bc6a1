import { Protocol } from "../../models";
/**
 * 添加日历
 * @see http://wiki.corpautohome.com/pages/viewpage.action?pageId=224268897
 */
export declare function saveCalendarEvent(params: SaveCalendarEventParams): Promise<Protocol<{
    id: string;
}>>;
export declare type SaveCalendarEventParams = {
    /**
     * 事件标题
     */
    title: string;
    /**
     * 事件开始日期(格林威治时间), 示例: `2021-05-20T21:46:00.000Z`
     */
    startDate: string;
    /**
     * 事件结束日期(格林威治时间), 示例: `2021-05-20T21:46:00.000Z`
     */
    endDate: string;
    /**
     * 备注
     */
    notes?: string;
    /**
     * 闹钟提醒
     */
    alarms?: Array<{
        /**
         * 如果给出一个字符串，将设置一个带有绝对日期的警报。
         *
         * 如果给定了一个数字，则将设置一个与开始日期有相对偏移量（以分钟为单位）的警报。
         */
        date: number | string;
    }>;
};
