export declare type AppSchemeParams = {
    /**
     * H5页面loading样式 （11.8.0版本启用）
     * 0：蓝条进度条样式（默认）
     * 1：小恐龙loadding样式
     */
    loadtype?: number;
    /**
     * 转屏控制参数
     * 0：固定竖屏
     * 1：竖向，向左横，向右横（Android端：随传感器自动转屏）
     * 2：向左横，向右横（Android端固定横屏）
     */
    _ahrotate?: number;
    /**
     * 是否全屏
     * 0：默认正常展示（状态栏+标题栏）
     * 1：正常展示状态栏、标题栏，展示叉号返回按钮（横屏不显示导航栏）
     * 2：全屏并隐藏浏览器的标题栏，展示圆形返回按钮
     * 3：隐藏返回按钮（只Android用）
     * 4：隐藏浏览器标题栏，用于主页的第四个tab的样式（不对业务线使用）
     * 5：展示系统状态栏、圆形返回按钮；隐藏标题栏（10.8.5版本启用）
     */
    navigationbarstyle?: number;
    /**
     * 主标题颜色 6位或8位颜色值
     */
    titlecolor?: string;
    /**
     * 标题设置url对应图片, 与标题颜色同时设置时优先显示
     */
    titleImageUrl?: string;
    /**
     * 副标题
     */
    subtitle?: string;
    /**
     * 标题栏背景色 6位或8位颜色值 11.8.0版本启用
     */
    bgcolor?: string;
    /**
     * 标题栏固定透明度, 0-1之间的小数
     */
    navalpha?: number;
    /**
     * 设置进入当前H5页面referer
     */
    Referer?: string;
    [key: string]: string | number | boolean | undefined;
};
/**
 * 获取跳转链接(如果是主软环境会添加协议)
 * @see http://wiki.corpautohome.com/pages/viewpage.action?pageId=208035756
 * @param url 链接地址
 * @param schemeParams 协议跳转链接参数(主软环境下, 该参数默认使用小恐龙样式)
 */
export declare function getLocationHrefLink(url: string, schemeParams?: AppSchemeParams): string;
