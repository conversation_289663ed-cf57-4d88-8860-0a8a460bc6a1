/**
 * 数据协议-获取线索城市
 * @param params - 参数
 * @param timeout - 协议执行超时时间
 * @see {@link http://wiki.corpautohome.com/pages/viewpage.action?pageId=204400499}
 */
export declare function getClueCity(params: GetClueCityParams, timeout?: number): Promise<GetClueCityResult>;
export declare type GetClueCityParams = {
    /**
     * 线索页面手机号码，非必要参数
     */
    phone?: string;
    /**
     * 请求接口超时时长，单位秒，非必要参数，默认值20
     */
    timeout?: number;
};
export interface GetClueCityResult {
    /**
     * 省份Id，默认值0
     */
    "provinceid": number;
    /**
     * 省份名称，默认值字符串""
     */
    "provincename": string;
    /**
     * 城市Id，默认值0
     */
    "cityid": number;
    /**
     * 城市名称，默认值字符串""
     */
    "cityname": string;
    /**
     * 返回城市类型
     * 2是客户端缓存的上次GPS定位成功城市
     * 3是线索页面手机号码归属城市
     * 4是客户端缓存的全局用户选择城市
     * 5是用户注册账号手机号码归属城市
     * 6是默认城市，即Id是0，名称是""
     */
    "citytype": number;
}
