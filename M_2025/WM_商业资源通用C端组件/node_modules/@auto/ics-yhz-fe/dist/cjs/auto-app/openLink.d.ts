import { AppSchemeParams } from './getLocationHrefLink';
export declare type OpenLinkOptions = {
    /**
     * 拨打电话权限说明
     */
    telDesc?: string;
    /**
     * 延迟多长时间后调整(单位毫秒)
     */
    delay?: number;
    /**
     * 主软协议打开携带的参数
     * *如果不传, 该参数默认为: `{ loadtype: 0, Referer: location.href }`*
     */
    schemeParams?: AppSchemeParams;
};
export interface OpenLinkFn {
    /**
     * 打开链接(主软环境使用协议打开)
     * @param link 链接地址, 支持 https://xxxx, 主软链接(autohome://url), 电话(tel:xxxx)
     */
    (link: string): void;
    /**
     * 打开链接(主软环境使用协议打开, 支持电话协议 tel:xxx)
     * @param link 链接地址, 支持 https://xxxx, 主软链接(autohome://url), 电话(tel:xxxx)
     * @param telDesc 拨打电话的授权说明
     * @deprecated 不建议使用
     */
    (link: string, telDesc: string): void;
    /**
     * 打开链接(主软环境使用协议打开, 支持电话协议 tel:xxx)
     * @param link 链接地址, 支持 https://xxxx, 主软链接(autohome://url), 电话(tel:xxxx)
     * @param options 选项
     */
    (link: string, options: OpenLinkOptions): void;
}
/**
 * 打开链接(主软环境使用协议打开, 支持电话协议 tel:xxx)
 * @param link 链接地址, 支持 https://xxxx, 主软链接(autohome://url), 电话(tel:xxxx)
 * @param options 选项
 */
export declare const openLink: OpenLinkFn;
