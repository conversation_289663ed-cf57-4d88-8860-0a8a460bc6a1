export declare type AutoAppInfo = {
    isApp: boolean;
    platform: 'iphone' | 'ios' | 'ipad' | 'android' | '' | "harmony";
    appKey: string;
    version: string;
    __detectByCookie?: boolean;
};
/**
 * 获取之家App信息(支持极速版、报价等App)
 * @param userAgent - User Agent(默认为 navigator.userAgent)
 * @param cookie - Cookie值(默认当前文档的cookie)
 * @returns App信息
 * @public
 */
export declare function getAutoAppInfo(userAgent?: string, cookie?: string): AutoAppInfo;
/**
 * 获取 APP 信息
 * @property {boolean} isApp 是否在汽车之家 App 中打开
 * @property {null | "iphone" | "android"} platform app 平台 (iphone|android)
 * @property {null|string} version app 版本
 * @returns {{isApp: boolean, platform: null | 'iphone' | 'ios' | 'ipad' | 'android', version: null | string}} APP 相关信息
 * @see http://npm.corpautohome.com/package/@auto/am-scheme
 */
export declare function getAppInfo(): AutoAppInfo;
