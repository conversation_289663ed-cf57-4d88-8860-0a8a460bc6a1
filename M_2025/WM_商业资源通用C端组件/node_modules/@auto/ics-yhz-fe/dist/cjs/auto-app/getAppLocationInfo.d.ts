import { Protocol } from "../models/index";
/**
 * getlocationinfo 返回数据
 */
declare type AppLocationInfoResult = Protocol<{
    WGSLongitude: string;
    WGSLatitude: string;
    GCJLatitude: string;
    GCJLongitude: string;
    BDLongitude: string;
    BDLatitude: string;
    provinceid: string;
    provincename: string;
    cityid: string;
    cityname: string;
    districtid: string;
    districtname: string;
    selectprovinceid: string;
    selectprovincename: string;
    selectcityid: string;
    selectcityname: string;
}>;
/**
 * 获取主App缓存位置信息( selectcityid: 用户选择的城市信息, cityid: 定位的城市信息)
 * *实时定位信息请使用 `getRealTimeLocation` 方法*
 * @param ignoreCityIdValid 忽略城市id校验
 * @see http://wiki.corpautohome.com/pages/viewpage.action?pageId=89746843
 * @see http://wiki.corpautohome.com/pages/viewpage.action?pageId=89742440
 */
export declare function getAppLocationInfo(ignoreCityIdValid?: boolean): Promise<AppLocationInfoResult["result"]>;
export {};
