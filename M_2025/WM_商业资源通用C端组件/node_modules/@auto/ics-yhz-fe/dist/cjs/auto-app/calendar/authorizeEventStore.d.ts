import { Protocol } from "../../models";
/**
 * 申请日历权限
 * @param params 申请权限参数
 * @see http://wiki.corpautohome.com/pages/viewpage.action?pageId=224268890
 * @returns `denied`: 拒绝 `authorized`: 允许
 */
export declare function authorizeEventStore(params: AuthorizeEventStoreParams): Promise<Protocol<{
    status: "authorized" | "denied";
}>>;
export declare type AuthorizeEventStoreParams = {
    readPermissionDesc?: string;
    writePermissionDesc?: string;
};
