import { Protocol } from "../models/index";
/**
 * 设置App原生页 右上角按钮和页面标题
 * @see http://wiki.corpautohome.com/pages/viewpage.action?pageId=80987615
 */
export declare function updateAppActionBarInfo(param: UpdateAppActionBarInfoParam): Promise<Protocol<any>>;
export declare type UpdateAppActionBarInfoParam = {
    /**
     * H5页面 客户端显示的 标题
     * 该标题逻辑为，首先获取Html中<title>标签中的内容，并进行显示
     * 如果页面中有该方法，并且该参数值不为空，
     * 则将页面title替换为该参数值
     */
    title: string;
    /**
     * 用户自定义右上角按钮数组，如果无按钮 则为[]
     * title:右上角按钮标题
     * scheme：用户点击按钮后，打开的原生页面scheme
     * 目前最多只支持一个按钮
     */
    addmenulist?: Array<{
        title: string;
        scheme: string;
    }>;
    /**
     * 客户端固定按钮列表，默认隐藏。如需要显示则将按钮标识加入该列表中。
     * 目前客户端固定按钮如下：
     * @see http://wiki.corpautohome.com/pages/viewpage.action?pageId=80987543 share 显示 [分享按钮] 后点我设置分享信息
     * @see http://wiki.corpautohome.com/pages/viewpage.action?pageId=80987570 city 显示[选择城市]后 点我获取选择的内容
     */
    stablemenulist?: Array<"share" | "city">;
    /**
     * 自定义图片的按钮列表 (目前仅支持一个)
     * imgurl: 按钮上显示的图片地址 (必传)
     * callbackjs: 按钮点击后回调的 js 方法名. (必传,h5 自己实现该方法)
     * iOS 仅支持 WK 浏览器 图片 size(24,24) 使用素材时注意
     */
    customimagebuttonlist?: Array<{
        imgurl: string;
        callbackjs: string;
    }>;
    /**
     * text: 搜索栏上显示的文字
     * callbackjs: 按钮点击后回调的 js 方法名. (必传,h5 自己实现该方法)
     */
    centersearch?: {
        text: string;
        callbackjs: string;
    };
};
