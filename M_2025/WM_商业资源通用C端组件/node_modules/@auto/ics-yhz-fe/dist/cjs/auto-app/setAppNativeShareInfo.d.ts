import { BasicPrimitiveType, IMapDeep } from "../models";
/**
 * ref http://wiki.corpautohome.com/pages/viewpage.action?pageId=208035622
 * 设置主软分享信息
 * @param param 分享信息
 * @returns
 */
export declare function setAppNativeShareInfo(param: SetAppNativeShareInfoParam): Promise<any>;
export declare type SetAppNativeShareInfoParam = {
    /**
     * 所有平台：all
     * 多个平台如上:使用 & 连接, 比如 weixin&weixincircle&weibo
     * 微信：weixin
     * 微信朋友圈：weixincircle
     * 新浪微博：weibo
     * QQ好友：qq
     * QQ空间：qqzone
     * 支付宝好友：zhifubao
     */
    platform: "all" | "weixin" | "weixincircle" | "weibo" | "qq" | "qqzone" | "zhifubao";
    /**
     * 分享后打开的H5链接
     */
    url: string;
    /**
     * 分享到各个分享平台的title
     */
    title: string;
    /**
     * 分享的内容
     */
    content: string;
    /**
     * 分享图片(网络图片链接)
     */
    imgurl?: string;
    /**
     * 图片二进制，如果缺省该字段，走原有逻辑。否则走图片分享逻辑。本地图片base 64格式的数据
     */
    binaryimage?: string;
    /**
     * 10.5.5版本新增字段，sharetype取值："pic"：代表纯图片分享。（其他走默认，后续有新类型，再新增）
     */
    sharetype?: string;
    /**
     * 分享项相应参数配置的json字符串，可通过约定的分享平台配置具体的参数。额外的参数可选填，如只添加微信分享项的以微信小程序则只需加 wx_mini_program 参数即可。
     */
    options?: IMapDeep<BasicPrimitiveType>;
};
