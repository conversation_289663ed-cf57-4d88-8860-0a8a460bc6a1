/**
 * 注册App打开的H5页面回显时的事件.(注册完成之后可以通过监听 document.body.addEventListener("on-display-when-back-in-app", function() {}) 事件, 也可以传递回调函数.)
 * @param {function} callback 回显时的回调函数(可不传)
 * @returns {String} 绑定的事件名
 * @example
 *
 * //方式1
 * onDisplayWhenBackInApp();
 * document.body.addEventListener("on-display-when-back-in-app", function() {
 *    alert("back again"); //自定义代码
 * });
 *
 * //方式2
 * onDisplayWhenBackInApp(function(){
 *    alert("back again"); //自定义代码
 * });
 */
export declare function onDisplayWhenBackInApp(callback: () => void): string;
