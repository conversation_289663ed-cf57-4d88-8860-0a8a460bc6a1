import { Protocol } from "../../models";
export declare type OnAppShakeParams = {
    /**
     * 摇一摇开始执行
     */
    onShake: OnAppShakeCallback;
};
export declare type OnAppShakeResult = {
    /**
     * 取消监听摇一摇
     */
    offShake: () => void;
};
export declare type OnAppShakeDef = {
    (params: OnAppShakeParams): OnAppShakeResult;
    __hasInit?: boolean;
    __callbacks?: Array<OnAppShakeCallback>;
};
export declare type OnAppShakeCallback = (args: Protocol<{}>, callback: any) => void;
/**
 * 主软摇一摇的回调函数.
 *
 * **注意该方法需要在`startAppShake`调用执行执行**
 *
 * 经过实测, 用户摇一次:
 * * iOS平台会执行两次回调, 分别是
 *   * 摇一摇开始(`returncode: 1`)
 *   * 摇一摇结束(`returncode: 2`)
 * * Android平台会执行一次回调(`returncode: 3`)
 * <AUTHOR>
 * @date 2023-01-06
 */
export declare const onAppShake: OnAppShakeDef;
