/**
 * 当前是否是更新/相同版本(限制了必须存在 window.AHAPP.invokeNative 变量)
 * @param compareVersion 待比较版本
 * @deprecated
 */
export declare function currentIsNewOrSameVersion(compareVersion: string): boolean;
/**
 * 当前是否是更新/相同版本(仅判断版本号, 不判断ahapp.js是否引用)
 * @param  compareVersion 待比较版本
 */
export declare function currentIsNewOrSameVersion2(compareVersion: string): boolean;
/**
 * 当前是否是旧版本(限制了必须存在 window.AHAPP.invokeNative 变量)
 * @param compareVersion 待比较版本
 * @deprecated
 */
export declare function currentIsOldVersion(compareVersion: string): boolean;
/**
 * 当前是否是旧版本(仅比较版本号, 不判断是否引用 ahapp.js)
 * @param {String} compareVersion 待比较版本
 * @returns {Boolean}
 */
export declare function currentIsOldVersion2(compareVersion: string): boolean;
