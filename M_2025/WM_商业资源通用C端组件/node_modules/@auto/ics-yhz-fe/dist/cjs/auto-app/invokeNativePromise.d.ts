/**
 * AHAPP.invokeNative 调用封装
 * 比如拨打电话: invokeNativePromise("tel", {tel: "110100"}).then(data => console.log('success'))
 * @param action AHAPP.invokeNative 的第一个参数
 * @param params AHAPP.invokeNative 的第二个参数(不需要传递success/fail回调)
 * @param timeout 可选参数(单位ms), 指定时间内 AHAPP.invokeNative 未执行回调返回超时错误
 */
export declare function invokeNativePromise<T>(action: string, params: any, timeout?: number): Promise<T>;
