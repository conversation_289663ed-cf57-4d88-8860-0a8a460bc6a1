import { MapPrimitive } from "../models/MapPrimitive";
export declare type GetReactNativeLinkParams = {
    /**
     * RN 组件和路径(**不包含协议 `rn://`**), 比如: `Dealer_IcsCsh/CpsOrderBuTie`
     */
    component: string;
    /**
     * RN 组件的参数
     */
    parameters: MapPrimitive;
    /**
     * 是否禁用透明弹窗
     */
    disableTransparentBg?: boolean;
    /**
     * scheme 的参数
     */
    schemeParameters?: MapPrimitive;
};
/**
 * 生成 RN 链接
 * <AUTHOR>
 * @date 2024-05-20
 */
export declare function getReactNativeLink(options: GetReactNativeLinkParams): string;
