import { Protocol } from "../models";
/**
 * 获取主软设备信息
 * <AUTHOR>
 * @date 2022-05-05
 * @link http://wiki.corpautohome.com/pages/viewpage.action?pageId=80984953
 */
export declare function getDeviceInfo(): Promise<Protocol<GetDeviceInfoResult>>;
/**
 * 用户设备信息
 */
export interface GetDeviceInfoResult {
    /**
   *  设备唯一标识符
   */
    deviceid: string;
    /**
     *  平台号，iOS值为1，Android值为2
     */
    platform: number;
    /**
     *  应用号，主软件值为2
     */
    appkey: number;
    /**
     * 	应用版本号
     */
    appver: string;
    /**
     * 	系统版本号
     */
    sysver: string;
    /**
     *  屏幕分辨率
     */
    resolution: string;
    /**
     *  设备厂商
     */
    devicefirm: string;
    /**
     *  设备名称，iOS值为iPhone 6,2、iPhone 7,1等，具体机型需要自己转换
     */
    devicename: string;
    /**
     *  Android值为设备ANDROID_ID，iOS值为空，android特殊机型无法获取
     */
    androidid: string;
    /**
     *  设备Mac地址，iOS值为空
     */
    mac: string;
    /**
     *  Android值为设备IMEI，iOS值为空
     */
    imei: string;
    /**
     *  Android值为空，iOS值为设备IDFV
     */
    idfv: string;
    /**
     *  Android设备是否Root
     */
    root: boolean;
    /**
     *  iOS设备是否越狱
     */
    jailbreak: boolean;
    /**
     *  络类型，0表示WIFI，1表示4G，2表示3G，3表示2G，4表示未连接网络，-1表示未知
     */
    network: number;
    /**
     *  运营商识别码例如:中国移动：46000 46002 中国联通：46001 中国电信：46003 ，46011
     */
    carrier: number;
    /**
     *  内存大小，单位为字节
     */
    memsize: number;
    /**
     *  umsAgent访次 9.13.5及以后版本支持
     */
    sessionid: string;
    /**
     *  设备是否支持AR 9.14.0及以后版本支持
     */
    supportAR: boolean;
    /**
     *  第三方厂商TalkingData设备号 10.3.0及以后版本支持
     */
    talkingdatadeviceid: string;
    /**
     *  手机总存储空间 单位byte 10.3.0及以后版本支持
     */
    diskspace: number;
    /**
     *  电池充电状态 0表示未知，1表示非充电状态，2表示充电状态，3表示充满状态 10.3.0及以后版本支持
     */
    batterystate: number;
    /**
     *  是否是模拟器
     */
    issimulator: boolean;
    /**
     *  合规勾选状态，0不勾选，1勾选
     */
    compliancestate: number;
    /**
     *  获取个性化定向推送开关状态
     */
    personalizedPush: boolean;
    /**
     *  获取合作伙伴数据展现广告开关状态
     */
    partnerAd: boolean;
    /**
     * 允许向您展示程序化广告开关状态
     */
    systemAd: boolean;
}
