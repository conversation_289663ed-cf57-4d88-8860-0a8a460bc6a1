export declare type ShareInnerAutoAppParams = {
    /**
     * 分享的平台
     */
    platform: "all" | Array<ShareInnerAutoAppPlatform>;
    /**
     * 分享后打开的H5链接
     */
    url: string;
    /**
     * 分享到各个分享平台的title
     */
    title: string;
    /**
     * 分享的内容
     */
    content: string;
    /**
     * 分享图片，如果图片为空，则会分享APP LOGO默认图
     */
    imgurl: string;
    /**
     * 图片二进制，如果缺省该字段，走原有逻辑，否则走图片分享逻辑。此字段10.3.0版本增加。注意：纯图片分享时，需要设置两个字段的数值，imgurl和binaryimage
     * 其中imgurl为android图片分享需要的值，binaryimage为ios图片分享需要的值
     */
    binaryimage?: string;
    /**
     * 分享平台中有车友圈时，该字段必须赋值, 注意：要对值进行编码，键无需编码，下面是示例：参数说明请参照 分享车友圈参数说明
     */
    intenturl?: string;
    /**
     * 传入海报图片的 url.
     * 当有此字段时, 会调起一个带有海报分享按钮的分享控件, 点击分享海报会去下载 url 的图片.
     * 图片下载完会自动再次弹起分享控件, 此时上面只有 微信,朋友圈,保存到相册三个按钮. 并且按钮不支持自定义.
     */
    posterurl?: string;
    /**
     * app端直接跳转微信小程序时目录小程序相关参数的json串。此功能 platform 只能指定为weixin。9.9.0及以后版本支持。
     * 具体参数如下：
     * userName：要跳转的小程序userName 必填
     * path：要跳转的小程序path 必填
     * type：要跳转的小程序对应的版本，选填 0：正式版本（默认） 1：测试版本 2：体验版本
     * jsonk串格式。如： {"userName":"gh_bfcd071dba17","path":"view\/article\/show?articleid=926017&type=0","type":0}
     *
     * @deprecated 请使用那个 options.wx_mini_program
     */
    wx_mini_program?: {
        userName: string;
        path: string;
    };
    /**
     * 分享项相应参数配置的json字符串，可通过约定的分享平台配置具体的参数。额外的参数可选填，如只添加微信分享项的以微信小程序则只需加 wx_mini_program 参数即可。
     * 微信小程序
     * 参数 wx_mini_program：
     * @example
     ```javascript
     {
        userName: 'gh_bfcd071dba17',
        path: 'view/article/show?articleid=926017&type=0',
        type: 0 //要跳转的小程序对应的版本，选填 0：正式版本（默认） 1：测试版本 2：体验版本
      }
      ```
     */
    options?: {
        wx_mini_program?: {
            userName: string;
            path: string;
            /**
             * 要跳转的小程序对应的版本，选填
             * * 0: 正式版本（默认）
             * * 1：测试版本
             * * 2：体验版本
             */
            type?: number;
        };
    };
    /**
     * 此字段在客户端10.2.5版本增加。H5页面请考虑版本兼容。此功能对应的demo：[share.html](http://wiki.corpautohome.com/download/attachments/80987503/share.html?version=1&modificationDate=1571286285000)
     * json字符串, 格式如下:
  ```javascript
  [{
    "title":"标题",
    "image":"图片的URL",
    "jsFunction":"需要回调的JS方法"
  }, {
    "title":"标题",
    "image":"图片的URL",
    "jsFunction":"需要回调的JS方法"
  }]
  ```
  
     * 注意：jsFunction参数，是H5中定义点击按钮的回调方法，需要用下面的方式注册后，原生才能正确调用：
  ```javascript
  // 先声明一个方法给ios使用。
  function funcSave(args, callback) {
      //H5 指定超时时间内返回原生回调
      if(callback){
          callback("ok");
      }
  }
  // 在把这个方法注册给js协议，供Android使用
  AHJavascriptBridge.bindMethod("funcSave", funcSave);
  window.funcSave=funcSave;
  ```
     * 用于H5在原生的分享弹窗中，自定义按钮的功能，比如保存图片。保存图片的功能，需要在按钮的点击回调的js方法里调用保存图片的api：H5页面中的保存图片到本地相册
     */
    extendList?: string;
};
/**
 * 客户端支持的平台如下
 * * 微信：weixin
 * * 微信朋友圈：weixincircle
 * * 新浪微博：weibo
 * * QQ好友：qq
 * * QQ空间：qqzone
 * * 支付宝好友：zhifubao
 * * 车友圈:carfriend
 *
 * 平台参数使用如下
 * 1. platform参数值只有一个值，比如weixin，则客户端直接掉起微信分享
 * 2. platform参数值有多个值，比如weixin&weixincircle&weibo，则客户端弹出分享面板，面板中只有微信、朋友圈、微博
 * 3. platform参数值为all，则客户端弹起分享面板，面板中是所有能分享的平台。
 * 4. all 默认不包含车友圈及举报，如需要定制车友圈需要进行platform定制
 * 5. platform只指定weixin且传入微信小程序相关参数（wx_mini_program）时走跳转微信小程序功能。具体参数是包括目标跳转小程序的userName,path及type参数的json串。
 */
export declare type ShareInnerAutoAppPlatform = "weixin" | "weixincircle" | "weibo" | "qq" | "qqzone" | "zhifubao" | "carfriend";
/**
 * H5分享
 * * wiki http://wiki.corpautohome.com/pages/viewpage.action?pageId=80987503
 * @date 2022-10-09
 */
export declare function shareInnerAutoApp(params: ShareInnerAutoAppParams): Promise<unknown>;
